#!/usr/bin/env python3
"""
Test Suite for Assistant.py

This test suite allows manual testing of all functions in the Assistant class.
You can modify the test data and run individual tests to see the results.

Usage:
    python testSuite.py
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.assistant import Assistant
from shared_types.context_variables import ContextVariables
from database.connection import DatabaseManager
from config.logging import setup_logging
import logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


class AssistantTestSuite:
    def __init__(self):
        self.assistant = None
        self.db_manager = DatabaseManager()
        self.test_context_vars = {
            "phone_number": "+14434231722",
            "customer": None,
            "appointment_id": None
        }

    async def setup_database(self):
        """Initialize database connection"""
        print("🔧 Setting up Database connection...")
        try:
            await self.db_manager.initialize()
            print("✅ Database connection established!")
            return True
        except Exception as e:
            print(f"❌ Failed to setup database: {e}")
            return False

    async def cleanup_database(self):
        """Close database connections"""
        try:
            await self.db_manager.close()
            print("🔒 Database connections closed")
        except Exception as e:
            print(f"⚠️  Error closing database: {e}")

    async def setup_assistant(self):
        """Initialize the assistant with test context variables"""
        print("🔧 Setting up Assistant...")
        try:
            self.assistant = await Assistant.create(context_vars=self.test_context_vars)
            print("✅ Assistant setup complete!")
            return True
        except Exception as e:
            print(f"❌ Failed to setup assistant: {e}")
            return False

    def print_test_header(self, test_name: str, description: str):
        """Print a formatted test header"""
        print("\n" + "="*60)
        print(f"🧪 TEST: {test_name}")
        print(f"📝 Description: {description}")
        print("="*60)

    def print_test_result(self, result: Any):
        """Print formatted test result"""
        print(f"📊 RESULT:")
        print("-" * 40)
        if isinstance(result, (dict, list)):
            print(json.dumps(result, indent=2, default=str))
        else:
            print(result)
        print("-" * 40)

    async def test_get_customer_detail(self):
        """Test getting customer details by phone number"""
        self.print_test_header(
            "get_customer_detail", 
            "Retrieve customer details using phone number from context"
        )
        
        try:
            result = await self.assistant.get_customer_detail()
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_create_new_customer(self, first_name: str = "John", last_name: str = "Doe"):
        """Test creating a new customer"""
        self.print_test_header(
            "create_new_customer", 
            f"Create new customer: {first_name} {last_name}"
        )
        
        try:
            result = await self.assistant.create_new_customer(first_name, last_name)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_service_categories(self):
        """Test getting service categories"""
        self.print_test_header(
            "get_service_categories", 
            "Retrieve all available service categories"
        )
        
        try:
            result = await self.assistant.get_service_categories()
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_services_offered(self, service_category_id: str = "1"):
        """Test getting services for a specific category"""
        self.print_test_header(
            "get_services_offered", 
            f"Retrieve services for category ID: {service_category_id}"
        )
        
        try:
            result = await self.assistant.get_services_offered(service_category_id)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_service_detail(self, service_id: str = "1"):
        """Test getting details for a specific service"""
        self.print_test_header(
            "get_service_detail", 
            f"Retrieve details for service ID: {service_id}"
        )
        
        try:
            result = await self.assistant.get_service_detail(service_id)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_staffs(self, skill_ids: list = None):
        """Test getting available staff by skill IDs"""
        if skill_ids is None:
            skill_ids = ["1", "2"]
            
        self.print_test_header(
            "get_staffs", 
            f"Retrieve staff with skill IDs: {skill_ids}"
        )
        
        try:
            result = await self.assistant.get_staffs(skill_ids)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_staffs_by_appointment_id(self, appointment_id: str = "test-appointment-123"):
        """Test getting staff details by appointment ID"""
        self.print_test_header(
            "get_staffs_by_appointment_id", 
            f"Retrieve staff for appointment ID: {appointment_id}"
        )
        
        try:
            result = await self.assistant.get_staffs_by_appointment_id(appointment_id)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_staff_detail(self, staff_id: str = "1"):
        """Test getting details for a specific staff member"""
        self.print_test_header(
            "get_staff_detail", 
            f"Retrieve details for staff ID: {staff_id}"
        )
        
        try:
            result = await self.assistant.get_staff_detail(staff_id)
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_get_prev_appointment(self):
        """Test getting previous appointments for the customer"""
        self.print_test_header(
            "get_prev_appointment", 
            "Retrieve previous appointments for current customer"
        )
        
        try:
            # First ensure we have a customer in context
            if not self.assistant.context_vars.get("customer"):
                print("⚠️  No customer in context. Creating test customer first...")
                await self.test_create_new_customer("Test", "Customer")
            
            result = await self.assistant.get_prev_appointment()
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    async def test_create_or_reschedule_appointment(self, 
                                                   staff_id: str = "1",
                                                   service_id: str = "1", 
                                                   appointment_date: str = "2025-06-28T14:00:00Z",
                                                   appointment_time: str = "14:00:00+00:00",
                                                   amount: int = 100,
                                                   notes: str = "Test appointment"):
        """Test creating or rescheduling an appointment"""
        self.print_test_header(
            "create_or_reschedule_appointment", 
            f"Create appointment - Staff: {staff_id}, Service: {service_id}, Date: {appointment_date}"
        )
        
        try:
            # Mock RunContext for testing
            class MockRunContext:
                class MockSession:
                    async def generate_reply(self, instructions):
                        print(f"🤖 Mock reply: {instructions}")
                
                def __init__(self):
                    self.session = self.MockSession()
            
            mock_context = MockRunContext()
            
            result = await self.assistant.create_or_reschedule_appointment(
                staff_id=staff_id,
                service_id=service_id,
                appointment_date=appointment_date,
                appointment_time=appointment_time,
                amount=amount,
                notes=notes,
                runContext=mock_context
            )
            self.print_test_result(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")

    def print_menu(self):
        """Print the test menu"""
        print("\n" + "🧪 ASSISTANT TEST SUITE MENU" + "\n")
        print("1.  Test get_customer_detail")
        print("2.  Test create_new_customer")
        print("3.  Test get_service_categories")
        print("4.  Test get_services_offered")
        print("5.  Test get_service_detail")
        print("6.  Test get_staffs")
        print("7.  Test get_staffs_by_appointment_id")
        print("8.  Test get_staff_detail")
        print("9.  Test get_prev_appointment")
        print("10. Test create_or_reschedule_appointment")
        print("11. Run all tests")
        print("12. Modify test data")
        print("0.  Exit")
        print("\nEnter your choice: ", end="")

    def modify_test_data(self):
        """Allow user to modify test data"""
        print("\n📝 MODIFY TEST DATA")
        print("-" * 30)
        print(f"Current phone number: {self.test_context_vars['phone_number']}")
        new_phone = input("Enter new phone number (or press Enter to keep current): ").strip()
        if new_phone:
            self.test_context_vars['phone_number'] = new_phone
            
        print(f"Current appointment ID: {self.test_context_vars['appointment_id']}")
        new_appt_id = input("Enter new appointment ID (or press Enter to keep current): ").strip()
        if new_appt_id:
            self.test_context_vars['appointment_id'] = new_appt_id
            
        print("✅ Test data updated!")

    async def run_all_tests(self):
        """Run all tests sequentially"""
        print("\n🚀 RUNNING ALL TESTS...")
        
        await self.test_get_customer_detail()
        await self.test_create_new_customer()
        await self.test_get_service_categories()
        await self.test_get_services_offered()
        await self.test_get_service_detail()
        await self.test_get_staffs()
        await self.test_get_staff_detail()
        await self.test_get_prev_appointment()
        await self.test_create_or_reschedule_appointment()
        
        print("\n✅ ALL TESTS COMPLETED!")

    async def run_interactive_menu(self):
        """Run the interactive test menu"""
        # Setup database and assistant
        if not await self.setup_database():
            return
        if not await self.setup_assistant():
            await self.cleanup_database()
            return

        try:
            while True:
                self.print_menu()
                try:
                    choice = input().strip()

                    if choice == "0":
                        print("👋 Goodbye!")
                        break
                    elif choice == "1":
                        await self.test_get_customer_detail()
                    elif choice == "2":
                        first_name = input("Enter first name (default: John): ").strip() or "John"
                        last_name = input("Enter last name (default: Doe): ").strip() or "Doe"
                        await self.test_create_new_customer(first_name, last_name)
                    elif choice == "3":
                        await self.test_get_service_categories()
                    elif choice == "4":
                        category_id = input("Enter service category ID (default: 1): ").strip() or "1"
                        await self.test_get_services_offered(category_id)
                    elif choice == "5":
                        service_id = input("Enter service ID (default: 1): ").strip() or "1"
                        await self.test_get_service_detail(service_id)
                    elif choice == "6":
                        skill_ids_input = input("Enter skill IDs separated by commas (default: 1,2): ").strip()
                        if skill_ids_input:
                            skill_ids = [id.strip() for id in skill_ids_input.split(",")]
                        else:
                            skill_ids = ["1", "2"]
                        await self.test_get_staffs(skill_ids)
                    elif choice == "7":
                        appt_id = input("Enter appointment ID (default: test-appointment-123): ").strip() or "test-appointment-123"
                        await self.test_get_staffs_by_appointment_id(appt_id)
                    elif choice == "8":
                        staff_id = input("Enter staff ID (default: 1): ").strip() or "1"
                        await self.test_get_staff_detail(staff_id)
                    elif choice == "9":
                        await self.test_get_prev_appointment()
                    elif choice == "10":
                        print("Enter appointment details:")
                        staff_id = input("Staff ID (default: 1): ").strip() or "1"
                        service_id = input("Service ID (default: 1): ").strip() or "1"
                        appt_date = input("Appointment date (default: 2025-06-28T14:00:00Z): ").strip() or "2025-06-28T14:00:00Z"
                        appt_time = input("Appointment time (default: 14:00:00+00:00): ").strip() or "14:00:00+00:00"
                        amount = input("Amount (default: 100): ").strip()
                        amount = int(amount) if amount.isdigit() else 100
                        notes = input("Notes (default: Test appointment): ").strip() or "Test appointment"
                        await self.test_create_or_reschedule_appointment(staff_id, service_id, appt_date, appt_time, amount, notes)
                    elif choice == "11":
                        await self.run_all_tests()
                    elif choice == "12":
                        self.modify_test_data()
                        # Recreate assistant with new data
                        await self.setup_assistant()
                    else:
                        print("❌ Invalid choice. Please try again.")

                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
        finally:
            # Always cleanup database connections
            await self.cleanup_database()


async def main():
    """Main function to run the test suite"""
    print("🎯 ASSISTANT.PY TEST SUITE")
    print("=" * 50)
    print("This test suite allows you to manually test all functions")
    print("in the Assistant class with custom data.")
    print("=" * 50)

    test_suite = AssistantTestSuite()
    await test_suite.run_interactive_menu()


if __name__ == "__main__":
    asyncio.run(main())
